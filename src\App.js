import "./App.css";

function App() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-8">
          <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">
            Cattle Tracking Dashboard
          </div>
          <h1 className="block mt-1 text-lg leading-tight font-medium text-black">
            Welcome to your dashboard
          </h1>
          <p className="mt-2 text-gray-500">
            This is a test to verify that Tailwind CSS is working properly.
          </p>
          <div className="mt-4">
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Get Started
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
